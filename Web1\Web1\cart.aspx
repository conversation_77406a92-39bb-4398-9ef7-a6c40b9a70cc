﻿<%@ Page Title="" Language="C#" MasterPageFile="~/q.Master" AutoEventWireup="true" CodeBehind="cart.aspx.cs" Inherits="Web1.cart" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">

    <h2>鎴戠殑璐墿杞?/h2>

    <asp:GridView ID="GridView1" runat="server" AutoGenerateColumns="False"
        CellPadding="4" ForeColor="#333333" GridLines="None" Width="100%"
        OnRowCommand="GridView1_RowCommand" OnRowDataBound="GridView1_RowDataBound">
        <AlternatingRowStyle BackColor="White" />
        <Columns>
            <asp:BoundField DataField="CartItemId" HeaderText="ID" Visible="false" />
            <asp:TemplateField HeaderText="鍟嗗搧鍥剧墖">
                <ItemTemplate>
                    <asp:Image ID="img_product" runat="server" Height="50px" Width="50px" />
                </ItemTemplate>
            </asp:TemplateField>
            <asp:BoundField DataField="ProName" HeaderText="鍟嗗搧鍚嶇О" />
            <asp:BoundField DataField="ListPrice" HeaderText="鍗曚环" DataFormatString="{0:F2}" />
            <asp:TemplateField HeaderText="鏁伴噺">
                <ItemTemplate>
                    <asp:TextBox ID="txt_qty" runat="server" Text='<%# Eval("Qty") %>' Width="50px"></asp:TextBox>
                    <asp:Button ID="btn_update" runat="server" Text="鏇存柊"
                        CommandName="UpdateQty" CommandArgument='<%# Eval("CartItemId") %>'
                        BackColor="#28a745" ForeColor="White" Font-Size="12px" />
                </ItemTemplate>
            </asp:TemplateField>
            <asp:TemplateField HeaderText="灏忚">
                <ItemTemplate>
                    <asp:Label ID="lbl_subtotal" runat="server"
                        Text='<%# String.Format("{0:F2}", Convert.ToDecimal(Eval("ListPrice")) * Convert.ToInt32(Eval("Qty"))) %>'></asp:Label>
                </ItemTemplate>
            </asp:TemplateField>
            <asp:TemplateField HeaderText="鎿嶄綔">
                <ItemTemplate>
                    <asp:Button ID="btn_delete" runat="server" Text="鍒犻櫎"
                        CommandName="DeleteItem" CommandArgument='<%# Eval("CartItemId") %>'
                        BackColor="#dc3545" ForeColor="White" Font-Size="12px"
                        OnClientClick="return confirm('纭畾瑕佸垹闄よ繖涓晢鍝佸悧锛?);" />
                </ItemTemplate>
            </asp:TemplateField>
        </Columns>
        <EditRowStyle BackColor="#2461BF" />
        <FooterStyle BackColor="#507CD1" Font-Bold="True" ForeColor="White" />
        <HeaderStyle BackColor="#507CD1" Font-Bold="True" ForeColor="White" />
        <PagerStyle BackColor="#2461BF" ForeColor="White" HorizontalAlign="Center" />
        <RowStyle BackColor="#EFF3FB" />
        <SelectedRowStyle BackColor="#D1DDF1" Font-Bold="True" ForeColor="#333333" />
        <SortedAscendingCellStyle BackColor="#F5F7FB" />
        <SortedAscendingHeaderStyle BackColor="#6D95E1" />
        <SortedDescendingCellStyle BackColor="#E9EBEF" />
        <SortedDescendingHeaderStyle BackColor="#4870BE" />
    </asp:GridView>

    <br />

    <div style="text-align: right; font-size: 16px; font-weight: bold;">
        鎬昏锛?asp:Label ID="lbl_total" runat="server" Text="0.00" ForeColor="Red"></asp:Label> 鍏?    </div>

    <br />

    <div style="text-align: center;">
        <asp:Button ID="btn_continue" runat="server" Text="缁х画璐墿"
            OnClick="btn_continue_Click" BackColor="#6c757d" ForeColor="White"
            Font-Size="14px" Padding="8px 16px" CausesValidation="false" />
        <asp:Button ID="btn_checkout" runat="server" Text="缁撶畻"
            OnClick="btn_checkout_Click" BackColor="#007bff" ForeColor="White"
            Font-Size="14px" Padding="8px 16px" />
        <asp:Button ID="btn_clear" runat="server" Text="娓呯┖璐墿杞?
            OnClick="btn_clear_Click" BackColor="#dc3545" ForeColor="White"
            Font-Size="14px" Padding="8px 16px" CausesValidation="false"
            OnClientClick="return confirm('纭畾瑕佹竻绌鸿喘鐗╄溅鍚楋紵');" />
    </div>

    <br />
    <asp:Label ID="lbl_message" runat="server" Text="" Font-Size="14px"></asp:Label>

    <asp:Panel ID="panel_empty" runat="server" Visible="false" style="text-align: center; padding: 50px;">
        <h3>璐墿杞︽槸绌虹殑</h3>
        <p>鎮ㄨ繕娌℃湁娣诲姞浠讳綍鍟嗗搧鍒拌喘鐗╄溅銆?/p>
        <asp:Button ID="btn_shop" runat="server" Text="鍘昏喘鐗?
            OnClick="btn_shop_Click" BackColor="#007bff" ForeColor="White"
            Font-Size="14px" Padding="8px 16px" />
    </asp:Panel>

</asp:Content>

